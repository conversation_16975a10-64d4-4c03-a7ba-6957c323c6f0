#!/bin/bash

# EmoDubber Simple Dubbing Script
# Usage: ./dub.sh "Your text here" reference_audio.wav

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default paths (adjust these to your setup)
DEFAULT_MODEL="Pretrained_TTSmodel/TTS_model.ckpt"
DEFAULT_VOCODER="Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "EmoDubber Simple Dubbing Script"
    echo "================================"
    echo ""
    echo "Usage:"
    echo "  ./dub.sh \"Your text here\" reference_audio.wav"
    echo ""
    echo "Examples:"
    echo "  ./dub.sh \"Hello world\" man1.mp3"
    echo "  ./dub.sh \"How are you today?\" reference.wav"
    echo ""
    echo "The output will be automatically saved as 'dubbed_output.wav'"
    echo ""
}

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        print_error "File not found: $1"
        exit 1
    fi
}

# Function to check if Python script exists
check_python_script() {
    if [ ! -f "simple_dubbing_inference.py" ]; then
        print_error "simple_dubbing_inference.py not found in current directory"
        print_info "Make sure you're running this script from the EmoDubber directory"
        exit 1
    fi
}

# Function to check dependencies
check_dependencies() {
    # Check if Python is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 is not installed or not in PATH"
        exit 1
    fi
    
    # Check if required Python packages are available
    python3 -c "import torch, soundfile, numpy" 2>/dev/null || {
        print_error "Required Python packages not found"
        print_info "Please install: torch, soundfile, numpy"
        exit 1
    }
}

# Parse command line arguments
parse_args() {
    # Default values
    TEMPERATURE="0.667"
    STEPS="10"
    SPEAKING_RATE="1.0"
    DEVICE="auto"
    MODEL_PATH="$DEFAULT_MODEL"
    VOCODER_PATH="$DEFAULT_VOCODER"

    # Check for help
    if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]] || [[ $# -eq 0 ]]; then
        show_usage
        exit 0
    fi

    # Minimum required arguments (only text and ref_audio)
    if [[ $# -lt 2 ]]; then
        print_error "Missing required arguments"
        print_error "Usage: ./dub.sh \"Your text here\" reference_audio.wav"
        exit 1
    fi

    TEXT="$1"
    REF_AUDIO="$2"

    # Auto-generate output filename based on timestamp
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    OUTPUT="dubbed_output_${TIMESTAMP}.wav"
}

# Main function
main() {
    print_info "Starting EmoDubber Simple Dubbing..."
    
    # Parse arguments
    parse_args "$@"
    
    # Check dependencies
    print_info "Checking dependencies..."
    check_dependencies
    check_python_script
    
    # Check input files
    print_info "Validating input files..."
    check_file "$REF_AUDIO"
    check_file "$MODEL_PATH"
    check_file "$VOCODER_PATH"
    
    # Create output directory if needed
    OUTPUT_DIR=$(dirname "$OUTPUT")
    if [ "$OUTPUT_DIR" != "." ] && [ ! -d "$OUTPUT_DIR" ]; then
        print_info "Creating output directory: $OUTPUT_DIR"
        mkdir -p "$OUTPUT_DIR"
    fi
    
    # Show configuration
    print_info "Configuration:"
    echo "  📝 Text: '$TEXT'"
    echo "  🎵 Reference Audio: $REF_AUDIO"
    echo "  💾 Output: $OUTPUT"
    echo "  🌡️  Temperature: $TEMPERATURE"
    echo "  ⚙️  Steps: $STEPS"
    echo "  🗣️  Speaking Rate: $SPEAKING_RATE"
    echo "  💻 Device: $DEVICE"
    echo ""
    
    # Run the Python script
    print_info "Running dubbing inference..."
    python3 simple_dubbing_inference.py \
        --text "$TEXT" \
        --ref_audio_path "$REF_AUDIO" \
        --output_path "$OUTPUT" \
        --model_checkpoint "$MODEL_PATH" \
        --vocoder_checkpoint "$VOCODER_PATH" \
        --temperature "$TEMPERATURE" \
        --steps "$STEPS" \
        --speaking_rate "$SPEAKING_RATE" \
        --device "$DEVICE"
    
    # Check if output was created
    if [ -f "$OUTPUT" ]; then
        print_success "Dubbing completed successfully!"
        print_success "Output saved to: $OUTPUT"
        
        # Show file size
        FILE_SIZE=$(du -h "$OUTPUT" | cut -f1)
        print_info "Output file size: $FILE_SIZE"
    else
        print_error "Dubbing failed - output file not created"
        exit 1
    fi
}

# Make sure script is executable
if [ ! -x "$0" ]; then
    print_warning "Script is not executable. Making it executable..."
    chmod +x "$0"
fi

# Run main function with all arguments
main "$@"
