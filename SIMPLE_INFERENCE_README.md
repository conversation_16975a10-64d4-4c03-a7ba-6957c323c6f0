# Simple EmoDubber Inference

This directory contains simplified inference scripts for EmoDubber that make it easy to dub audio using reference audio and text input.

## Files

- `simple_dubbing_inference.py` - Main SimpleDubber class and command-line interface
- `example_usage.py` - Example scripts showing how to use the SimpleDubber
- `SIMPLE_INFERENCE_README.md` - This documentation file

## Quick Start

### 1. Prerequisites

Make sure you have:
- Downloaded the pretrained EmoDubber model (`Pretrained_TTSmodel/TTS_model.ckpt`)
- Downloaded the vocoder model (`Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder`)
- A reference audio file (e.g., `man1.mp3`)
- All required Python dependencies installed

### 2. Command Line Usage

```bash
python simple_dubbing_inference.py \
    --text "Hello, how are you today?" \
    --ref_audio_path "man1.mp3" \
    --output_path "output_dubbed.wav" \
    --model_checkpoint "Pretrained_TTSmodel/TTS_model.ckpt" \
    --vocoder_checkpoint "Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder"
```

### 3. Python API Usage

```python
from simple_dubbing_inference import SimpleDubber

# Initialize the dubber
dubber = SimpleDubber(
    model_checkpoint="Pretrained_TTSmodel/TTS_model.ckpt",
    vocoder_checkpoint="Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder",
    device="auto"
)

# Synthesize audio
output = dubber.synthesize(
    text="Hello, this is a test.",
    ref_audio_path="reference_audio.wav",
    temperature=0.667,
    steps=10,
    speaking_rate=1.0
)

# Save the result
dubber.save_audio(output, "output.wav")
```

### 4. Run Examples

```bash
python example_usage.py
```

## Parameters

### SimpleDubber.__init__()
- `model_checkpoint` (str): Path to EmoDubber model checkpoint
- `vocoder_checkpoint` (str): Path to vocoder checkpoint
- `device` (str): Device to use ('auto', 'cuda', 'cpu')

### SimpleDubber.synthesize()
- `text` (str): Text to synthesize
- `ref_audio_path` (str): Path to reference audio file
- `temperature` (float): Sampling temperature (0.1-1.0, default: 0.667)
  - Lower values = more stable/deterministic output
  - Higher values = more varied/expressive output
- `steps` (int): Number of ODE steps (default: 10)
  - More steps = higher quality but slower
  - Fewer steps = faster but potentially lower quality
- `speaking_rate` (float): Speaking rate control (default: 1.0)
  - Values > 1.0 = slower speech
  - Values < 1.0 = faster speech

## Current Limitations

⚠️ **Important Notes:**

1. **Dummy Features**: The current implementation uses dummy speaker embeddings and visual features (lip/face). For production use, you need to:
   - Replace `_extract_speaker_embedding()` with a real speaker encoder (e.g., GE2E, x-vector)
   - Replace `_create_dummy_features()` with actual lip and face feature extraction

2. **Visual Features**: EmoDubber requires lip movement and facial expression features. The current dummy implementation limits quality.

3. **Speaker Similarity**: Without proper speaker embedding extraction, the output may not match the reference speaker's voice characteristics.

## Improvements Needed

To make this production-ready:

1. **Add Speaker Encoder**: Integrate a trained speaker encoder to extract meaningful speaker embeddings from reference audio.

2. **Add Visual Feature Extraction**: 
   - Lip feature extraction from video or audio-to-visual mapping
   - Facial expression/emotion feature extraction

3. **Audio Preprocessing**: Add proper audio preprocessing (resampling, normalization) for reference audio.

4. **Error Handling**: Add robust error handling for various input formats and edge cases.

5. **Batch Processing**: Optimize for processing multiple texts efficiently.

## Example Output

When you run the examples, you'll get:
- `output_example1.wav` - Basic dubbing example
- `output_example2.wav` - Higher quality, slower speech example
- `batch_output_01.wav` to `batch_output_04.wav` - Batch processing results

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Use `--device cpu` or reduce batch size
2. **Model not found**: Check that checkpoint paths are correct
3. **Audio format errors**: Ensure reference audio is in a supported format (wav, mp3)
4. **Import errors**: Make sure you're running from the correct directory with EmoDubber_Networks in the path

### Performance Tips

- Use GPU for faster inference (`--device cuda`)
- Reduce `steps` for faster processing (trade-off with quality)
- Use lower `temperature` for more consistent results
- Process multiple texts in batch for efficiency

## Next Steps

1. Try the examples with your own text and reference audio
2. Experiment with different parameter values
3. Integrate real speaker and visual feature extractors
4. Adapt the code for your specific use case

For more advanced usage, refer to the original EmoDubber inference scripts in `EmoDubber_Networks/`.
