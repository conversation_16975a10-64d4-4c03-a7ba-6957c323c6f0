absl-py==2.1.0
aiofiles==23.2.1
aiohttp==3.9.5
aiosignal==1.3.1
alembic==1.13.1
altair==5.3.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
async-timeout==4.0.3
attrs==23.2.0
audioread==3.0.1
autopage==0.5.2
Babel==2.15.0
beautifulsoup4==4.12.3
bibtexparser==2.0.0b7
bleach==6.1.0
certifi==2024.6.2
cffi==1.16.0
cfgv==3.4.0
charset-normalizer==3.3.2
click==8.1.7
cliff==4.7.0
clldutils==3.22.2
cmaes==0.10.0
cmd2==2.4.3
colorama==0.4.6
colorlog==6.8.2
comm==0.2.2
conformer==0.3.2
contourpy==1.2.1
csvw==3.3.0
cycler==0.12.1
Cython==3.0.10
debugpy==1.8.2
decorator==5.1.1
defusedxml==0.7.1
diffusers==0.25.0
distlib==0.3.8
dlinfo==1.2.1
dnspython==2.6.1
docker-pycreds==0.4.0
docopt==0.6.2
einops==0.8.0
email_validator==2.2.0
exceptiongroup==1.2.1
executing==2.0.1
fastapi==0.111.0
fastapi-cli==0.0.4
fastjsonschema==2.20.0
ffmpy==0.3.2
filelock==3.15.4
fonttools==4.53.0
fqdn==1.5.1
frozenlist==1.4.1
fsspec==2024.6.0
gdown==5.2.0
gitdb==4.0.12
GitPython==3.1.44
gradio==3.43.2
gradio_client==0.5.0
greenlet==3.0.3
grpcio==1.64.1
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.23.4
hydra-colorlog==1.2.0
hydra-core==1.3.2
hydra-optuna-sweeper==1.2.0
identify==2.5.36
idna==3.7
importlib_metadata==8.0.0
importlib_resources==6.4.0
inflect==7.3.0
iniconfig==2.0.0
ipykernel==6.29.4
ipython==8.25.0
ipywidgets==8.1.3
isodate==0.6.1
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.4
jiwer==3.0.4
joblib==1.4.2
json5==0.9.25
jsonpointer==3.0.0
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.2
jupyter_core==5.7.2
jupyter_server==2.14.1
jupyter_server_terminals==0.5.3
jupyterlab==4.2.2
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.2
jupyterlab_widgets==3.0.11
kaldiio==2.18.0
kiwisolver==1.4.5
language-tags==1.2.0
lazy_loader==0.4
librosa==0.10.2.post1
lightning==2.3.0
lightning-utilities==0.11.3.post0
llvmlite==0.43.0
lxml==5.2.2
Mako==1.3.5
Markdown==3.6
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.0
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
more-itertools==10.3.0
mpmath==1.3.0
msgpack==1.0.8
multidict==6.0.5
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
nodeenv==1.9.1
notebook==7.2.1
notebook_shim==0.2.4
num2words==0.5.13
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.5.40
nvidia-nvtx-cu12==12.1.105
omegaconf==2.3.0
optuna==2.10.1
orjson==3.10.5
overrides==7.7.0
packaging==24.1
pandas==2.2.2
pandocfilters==1.5.1
parso==0.8.4
pbr==6.0.0
pexpect==4.9.0
phonemizer==3.2.1
pillow==10.3.0
piper-phonemize==1.1.0
platformdirs==4.2.2
pluggy==1.5.0
pooch==1.8.2
pre-commit==3.7.1
prettytable==3.10.0
prometheus_client==0.20.0
prompt_toolkit==3.0.47
protobuf==4.25.3
psutil==6.0.0
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.22
pydantic==2.7.4
pydantic_core==2.18.4
pydub==0.25.1
Pygments==2.18.0
pylatexenc==2.10
pyparsing==3.1.2
pyperclip==1.9.0
PySocks==1.7.1
pytest==8.2.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.9
pytorch-lightning==2.3.0
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.0.3
rapidfuzz==3.9.6
rdflib==7.0.0
referencing==0.35.1
regex==2024.5.15
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986==1.5.0
rfc3986-validator==0.1.1
rich==13.7.1
rootutils==1.0.7
rpds-py==0.18.1
safetensors==0.4.3
scikit-learn==1.5.0
scipy==1.14.0
seaborn==0.13.2
segments==2.2.1
semantic-version==2.10.0
Send2Trash==1.8.3
sentry-sdk==2.27.0
setproctitle==1.3.5
shellingham==1.5.4
six==1.16.0
smmap==5.0.2
sniffio==1.3.1
soundfile==0.12.1
soupsieve==2.5
soxr==0.3.7
SQLAlchemy==2.0.31
stack-data==0.6.3
starlette==0.37.2
stevedore==5.2.0
sympy==1.12.1
tabulate==0.9.0
tensorboard==2.17.0
tensorboard-data-server==0.7.2
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tinycss2==1.3.0
tomli==2.0.1
toolz==0.12.1
torch==2.3.1
torchaudio==2.3.1
torchmetrics==1.4.0.post0
torchvision==0.18.1
tornado==6.4.1
tqdm==4.66.4
traitlets==5.14.3
triton==2.3.1
typeguard==4.3.0
typer==0.12.3
types-python-dateutil==2.9.0.20240316
typing_extensions==4.12.2
tzdata==2024.1
ujson==5.10.0
Unidecode==1.3.8
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.2
uvicorn==0.30.1
uvloop==0.19.0
virtualenv==20.26.3
wandb==0.19.10
watchfiles==0.22.0
wcwidth==0.2.13
webcolors==24.6.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==3.0.3
wget==3.2
widgetsnbextension==4.0.11
yarl==1.9.4
zipp==3.19.2
