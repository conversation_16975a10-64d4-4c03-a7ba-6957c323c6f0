#!/bin/bash

# Quick EmoDubber - One-line dubbing command
# Usage: ./quick_dub.sh "text" ref_audio.wav

# Check arguments
if [ $# -ne 2 ]; then
    echo "❌ Usage: ./quick_dub.sh \"Your text here\" reference_audio.wav"
    echo ""
    echo "Examples:"
    echo "  ./quick_dub.sh \"Hello world\" man1.mp3"
    echo "  ./quick_dub.sh \"How are you today?\" reference.wav"
    exit 1
fi

TEXT="$1"
REF_AUDIO="$2"
OUTPUT="dubbed_$(date +%H%M%S).wav"

# Check if reference audio exists
if [ ! -f "$REF_AUDIO" ]; then
    echo "❌ Reference audio file not found: $REF_AUDIO"
    exit 1
fi

# Check if Python script exists
if [ ! -f "simple_dubbing_inference.py" ]; then
    echo "❌ simple_dubbing_inference.py not found"
    echo "Make sure you're in the EmoDubber directory"
    exit 1
fi

echo "🎬 Quick Dubbing..."
echo "📝 Text: '$TEXT'"
echo "🎵 Reference: $REF_AUDIO"
echo "💾 Output: $OUTPUT"
echo ""

# Run the dubbing
python3 simple_dubbing_inference.py \
    --text "$TEXT" \
    --ref_audio_path "$REF_AUDIO" \
    --output_path "$OUTPUT" \
    --model_checkpoint "Pretrained_TTSmodel/TTS_model.ckpt" \
    --vocoder_checkpoint "Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder" \
    --temperature 0.667 \
    --steps 10 \
    --speaking_rate 1.0 \
    --device auto

if [ -f "$OUTPUT" ]; then
    echo ""
    echo "✅ Done! Output saved as: $OUTPUT"
else
    echo "❌ Failed to generate audio"
    exit 1
fi
