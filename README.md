<p align="center">
  <img src="assets/EmoDubber_Logo.png" width="30%" />
</p>
<div align="center">
  <h3 class="papername"> 
    EmoDubber: Towards High Quality and Emotion Controllable Movie Dubbing </h3>
</div>


[![python](https://img.shields.io/badge/Python-3.10-blue)](https://github.com/GalaxyCong/DubFlow)
[![arXiv](https://img.shields.io/badge/arXiv-2406.06937-b31b1b.svg?logo=arXiv)](https://arxiv.org/pdf/2412.08988)
[![code](https://img.shields.io/badge/Github-Code-keygen.svg?logo=github)](https://github.com/GalaxyCong/DubFlow)
[![demo](https://img.shields.io/badge/GitHub-Demo%20page-orange.svg)](https://galaxycong.github.io/EmoDub/)



# 

# 🗒 TODO List
- [✓] Release EmoDubber's training and inference code (Basic Fuction). (Fixed on 5/27/2025)
- [✓] Upload pre-processed dataset features to Baidu Cloud and Google Cloud. (Done 5/27/2025)
- [✓] Release model checkpoint (Basic Fuction) to inference waveform. (Before 6/1/2025)
- [-] Release EmoDubber's emotion controlling code (Emotion Fuction). 
- [-] Provide metrics testing scripts (LSE-C, LSE-D, SECS, WER, MCD). 


![Illustration](./assets/model_fig_1.jpeg)

# Environment

1. Clone this repository:
```bash
git clone https://github.com/GalaxyCong/EmoDubber.git
cd EmoDubber
```
2. Create an environment
```bash
conda create -n emodub python=3.10 -y
conda activate emodub
```
3. Install python requirements: 
```bash
pip install -r requirements.txt
```
4. Install [monotonic_align](https://github.com/resemble-ai/monotonic_align)
```bash
pip install git+https://github.com/resemble-ai/monotonic_align.git
```
5. (Option) Last step. Download [trainer.py](https://drive.google.com/file/d/1pfMlL22zz8xDcLYtN_cUTp8lMUquN4zD/view?usp=sharing) to replace your ```anaconda3/envs/emodub/lib/python3.10/site-packages/lightning/pytorch/trainer/trainer.py```;  
Download [checkpoint_connector.py](https://drive.google.com/file/d/1KkLOEsmG82znIHOIN0iHEfqR6ZyLY8yb/view?usp=sharing) to replace your ```anaconda3/envs/emodub/lib/python3.10/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py```

(Note: If you want to train model from scratch, step 5 is required. If you only want to do inference, please ignore it. Step5 is used to prevent the error of Missing key(s) in state_dict (TTS_model.ckpt >> EmoDubber_all).  I avoid this problem by setting "strict=False" in torch lightning.) 


# Prepare Data Feature

When performing training, both Raw Audio and Prosessed Features need to be downloaded. 
When inferencing, only Prosessed Features are needed. 

## Chem 

- Chem 16KHz Raw Audio: [Google Drive](https://drive.google.com/file/d/1gPwUePAkJFZJ5Xm7YrOuxCQN5NIbG5iF/view?usp=sharing) || [Baidu Drive](https://pan.baidu.com/s/1RD7gzSs3XeimTJVJOkkjxA)(erap)


- Chem Prosessed Feature: [Google Drive](https://drive.google.com/file/d/1xwx5cD8t24JPu3_t-Rqwreg0iH6rj5a1/view?usp=sharing) || [Baidu Drive](https://pan.baidu.com/s/198s3QM_Fi47kCPe6umzxyA)(nriv)


## GRID

- GRID 16KHz Raw Audio: [Google Drive](https://drive.google.com/file/d/1V0wyhmEKwB4N58w0GO4AX9WSLFLn86LE/view?usp=sharing) || [Baidu Drive](https://pan.baidu.com/s/174hmeiareDW51IG_a58AFg)(xikd)

- GRID Prosessed Feature: [Google Drive](https://drive.google.com/file/d/13h_htzQwZfZDEtfQ6BF-Y-99uAiSxyEA/view?usp=sharing) || [Baidu Drive](https://pan.baidu.com/s/1vpoaiXfYnu5RPmu3OKmkew)(cbdy)



# Train Your Own Model

1. Ensure input path is correct (see ```configs/data/Chem_dataset.yaml``` or ```configs/data/GRID_dataset```).
2. Download [TTS_model.ckpt](https://drive.google.com/file/d/14NbtYF07fKxw13MQJfhtD2NwizRpJFY5/view?usp=sharing) (pretraining on LibriTTS-clean-100 dataset) and save it in ```Pretrained_TTSmodel``` folder. 
3. Finally, please stay in root directory, and run directly: 
```bash
python EmoDubber_Networks/Train_EmoDubber_Chem16K.py
```
or
```bash
python EmoDubber_Networks/Train_EmoDubber_GRID16K.py
```


# Our Checkpoints

We provide EmoDubber's checkpoints (Basic Fuction). Moreover, we also provide the audio generated by these checkpoints. They were used to compare with other SOTA dubbing baseline in main setting (Setting1 & Setting 2), .ie, without emotion control. It will hopefully facilitate future comparisons. 

The links are given below:

## Checkpoints on Chem dataset

- Checkpoint: [Google Drive](https://drive.google.com/file/d/1CqTZU98xmHMy9C9Q0gFHvGyaZjUifizO/view?usp=sharing) or [Baidu Drive](https://pan.baidu.com/s/1M6anLZ7DUMRvVGapwJ2aaQ)(sxus)

- Generated Result: [Google Drive](https://drive.google.com/file/d/1NQn2T5aLghUwknnQiJTp_5wecsnlkrOr/view?usp=sharing) or [Baidu Drive](https://pan.baidu.com/s/16cwOmLFr3FCH4fbuWoFBQA)(heu2)



## Checkpoints on GRID dataset

- Checkpoint: [Google Drive](https://drive.google.com/file/d/1RRf--kPzldhHro6jbauh2sP29iphIh-h/view?usp=sharing) or [Baidu Drive](https://pan.baidu.com/s/1_7Orv8ccIxa_WlZD--GR6Q)(hv9t)

- Generated Result: [Google Drive](https://drive.google.com/file/d/1zsGYLDA25AfSTPEJuIn9edp3jjbdFXTn/view?usp=sharing) or [Baidu Drive](https://pan.baidu.com/s/1gB-riJ1QYwpeCI7KdGTNCA)(2ibw)



# Inference

1. Download EmoDubber's [16k Hz Vocoder](https://drive.google.com/file/d/1XXCSYbIEjePcWT8jNwSWeLlIQf0M-2pp/view?usp=sharing) and save it to ```./Vocoder_16KHz``` folder. 


2. Run script for inference (stay in root): 

- For main setting1: 
```Shell
python EmoDubber_Networks/Inference_Chem_Unbatch_New_S1.py \
    --checkpoint_path [model_dir] \
    --vocoder_checkpoint_path [vocoder_dir] \
    --Val_list [script_dir] \
    --Silent_Lip [lip_dir]  \
    --Silent_Face [face_dir] \
    --Refence_audio [reference_dir] \
```

- For main setting2: 
```Shell
python EmoDubber_Networks/Inference_Chem_Unbatch_New_S2.py \
    --checkpoint_path [model_dir] \
    --vocoder_checkpoint_path [vocoder_dir] \
    --Val_list [script_dir] \
    --Silent_Lip [lip_dir]  \
    --Silent_Face [face_dir] \
    --Refence_audio [reference_dir] \
    --Set2_list [script2_dir] \
```

### Arguments
- `checkpoint_path`: Path to the directory containing checkpoint files. We have provided our checkpoints. 
- `vocoder_checkpoint_path`: Path to the vocoder that matches EmoDubber. Default in ```Vocoder_16KHz``` folder. 
- `Val_list`: Path to txt script.  Equal to `valid_filelist_path` in `./configs/data/*.yaml`. 
- `Silent_Lip`: Path to lip-motion. Equal to `lip_embedding_path` in `./configs/data/*.yaml`. 
- `Silent_Face`: Path to face feature. Equal to `VA_path` in `./configs/data/*.yaml`. 
- `Refence_audio`: Path to reference audio feature. Equal to `Speaker_GE2E_ID_path` in `./configs/data/*.yaml`. 
- `Set2_list`: Path to txt script of setting2, requried in running `Inference_Chem_Unbatch_New_S2.py` or `Inference_GRID_Unbatch_New_S2.py`. It aims to avoid using the target audio as the reference, the reference audio should come from another clip. `Set2_list` can be download [here](https://drive.google.com/drive/folders/1xYAwNrpDpER73v28o86NpC2GzgAhduiy?usp=sharing). 



# Emotion Controlling  
Under construction

## Training emotional expert classifier

We provide all checkpoints. Below is the checkpoint of our emotional expert classifier. 

👉 Five types of emotions (Recommendation): https://drive.google.com/drive/folders/1vSVTAkZsoinSlYgeVCvBXs5V2k-gIurV?usp=sharing


👉 Seven types of emotions (Recommendation): https://drive.google.com/drive/folders/1h0Y1TChA9vgX3_6u5GUrJK69n0rgKTRU?usp=sharing


Seven types of emotions with emotionless data augmentation: https://drive.google.com/drive/folders/1DuhQYe5FuowHBRMOFRhthrvPJBlfK_5E?usp=sharing


## Inference 

Under construction


# License

Code: MIT License


# Citing

If you find this helps your research, please consider citing:
```BibTeX
@article{cong2024emodubber,
  title={EmoDubber: Towards High Quality and Emotion Controllable Movie Dubbing},
  author={Cong, Gaoxiang and Pan, Jiadong and Li, Liang and Qi, Yuankai and Peng, Yuxin and Hengel, Anton van den and Yang, Jian and Huang, Qingming},
  journal={arXiv preprint arXiv:2412.08988},
  year={2024}
}
```

# Contact

My <NAME_EMAIL>


Any discussions and suggestions are welcome!


