#!/usr/bin/env python3
"""
Simple EmoDubber Inference Script
Dub audio using reference audio and text input.

Usage:
    python simple_dubbing_inference.py \
        --text "Hello, how are you today?" \
        --ref_audio_path "path/to/reference_audio.wav" \
        --output_path "output_dubbed.wav" \
        --model_checkpoint "path/to/model.ckpt" \
        --vocoder_checkpoint "path/to/vocoder.ckpt"
"""

import os
import sys
import argparse
import warnings
import numpy as np
import torch
import soundfile as sf
import json
from pathlib import Path

# Add EmoDubber_Networks to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'EmoDubber_Networks'))

# EmoDubber imports
from models.EmoDubber import EmoDubber_all
from hifigan.models import Generator as HiFiGAN
from hifigan.config import v1
from hifigan.denoiser import Denoiser
from hifigan.env import AttrDict
from text_fs import text_to_sequence

warnings.filterwarnings("ignore")


class SimpleDubber:
    def __init__(self, model_checkpoint, vocoder_checkpoint, device="auto"):
        """
        Initialize the SimpleDubber with model checkpoints.
        
        Args:
            model_checkpoint (str): Path to EmoDubber model checkpoint
            vocoder_checkpoint (str): Path to vocoder checkpoint  
            device (str): Device to use ('auto', 'cuda', 'cpu')
        """
        self.device = self._get_device(device)
        self.model = self._load_model(model_checkpoint)
        self.vocoder, self.denoiser = self._load_vocoder(vocoder_checkpoint)
        
    def _get_device(self, device):
        """Get the appropriate device."""
        if device == "auto":
            return torch.device("cuda" if torch.cuda.is_available() else "cpu")
        return torch.device(device)
    
    def _load_model(self, checkpoint_path):
        """Load the EmoDubber model."""
        print(f"Loading EmoDubber model from {checkpoint_path}")
        model = EmoDubber_all.load_from_checkpoint(checkpoint_path, map_location=self.device)
        model.eval()
        print("✓ EmoDubber model loaded successfully")
        return model
    
    def _load_vocoder(self, checkpoint_path):
        """Load the HiFiGAN vocoder."""
        print(f"Loading vocoder from {checkpoint_path}")

        # Load vocoder config
        config_path = "Vocoder_16KHz/config.json"
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                config = json.load(f)
        else:
            # Fallback to default config
            config = v1
            
        h = AttrDict(config)
        vocoder = HiFiGAN(h).to(self.device)
        
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        vocoder.load_state_dict(checkpoint["generator"])
        vocoder.eval()
        vocoder.remove_weight_norm()
        
        # Initialize denoiser
        denoiser = Denoiser(vocoder, mode="zeros")
        
        print("✓ Vocoder loaded successfully")
        return vocoder, denoiser
    
    def _process_text(self, text):
        """Convert text to sequence of phoneme IDs."""
        sequence = text_to_sequence(text, ['english_cleaners'])
        x = torch.IntTensor(sequence).long().unsqueeze(0).to(self.device)
        x_lengths = torch.tensor([x.shape[-1]], dtype=torch.long, device=self.device)
        return x, x_lengths
    
    def _extract_speaker_embedding(self, ref_audio_path):
        """
        Extract speaker embedding from reference audio.
        This is a simplified version - in practice you'd use a trained speaker encoder.
        """
        # For now, create a dummy speaker embedding
        # In a real implementation, you'd extract this using a speaker encoder like GE2E
        speaker_embedding = torch.randn(1, 256).to(self.device)  # Dummy embedding
        print("⚠ Using dummy speaker embedding - replace with actual speaker encoder")
        return speaker_embedding
    
    def _create_dummy_features(self, text_length):
        """
        Create dummy lip and face features.
        In practice, these would be extracted from video or estimated from audio.
        """
        # Create dummy lip embedding (visual features)
        lip_length = text_length * 4  # Approximate lip frames
        lip_embedding = torch.randn(1, lip_length, 512).to(self.device)
        lip_lengths = torch.tensor([lip_length], dtype=torch.long, device=self.device)
        
        # Create dummy face features (emotion/expression)
        face_features = torch.randn(1, lip_length, 256).to(self.device)
        
        print("⚠ Using dummy visual features - replace with actual lip/face extraction")
        return lip_embedding, lip_lengths, face_features
    
    def synthesize(self, text, ref_audio_path, temperature=0.667, steps=10, speaking_rate=1.0):
        """
        Synthesize dubbed audio from text and reference audio.
        
        Args:
            text (str): Text to synthesize
            ref_audio_path (str): Path to reference audio file
            temperature (float): Sampling temperature
            steps (int): Number of ODE steps
            speaking_rate (float): Speaking rate control
            
        Returns:
            dict: Dictionary containing synthesized waveform and metadata
        """
        print(f"Synthesizing: '{text}'")
        
        # Process text
        x, x_lengths = self._process_text(text)
        
        # Extract speaker embedding from reference audio
        speaker_embedding = self._extract_speaker_embedding(ref_audio_path)
        
        # Create dummy visual features (lip and face)
        lip_embedding, lip_lengths, face_features = self._create_dummy_features(x_lengths.item())
        
        # Synthesize mel-spectrogram
        with torch.inference_mode():
            output = self.model.synthesise(
                x=x,
                x_lengths=x_lengths,
                Lip=lip_embedding,
                lip_lengths=lip_lengths,
                n_timesteps=steps,
                temperature=temperature,
                spks=speaker_embedding,
                VAfeature=face_features,
                length_scale=speaking_rate,
            )
        
        # Convert mel to waveform using vocoder
        mel = output["mel"]
        with torch.inference_mode():
            waveform = self.vocoder(mel).clamp(-1, 1)
            if self.denoiser is not None:
                waveform = self.denoiser(waveform.squeeze(), strength=0.00025)
        
        waveform = waveform.cpu().squeeze().numpy()
        
        return {
            "waveform": waveform,
            "sample_rate": 16000,
            "mel": mel.cpu().numpy(),
            "rtf": output.get("rtf", 0.0)
        }
    
    def save_audio(self, output, filepath):
        """Save synthesized audio to file."""
        sf.write(filepath, output["waveform"], output["sample_rate"], "PCM_24")
        print(f"✓ Audio saved to {filepath}")


def main():
    parser = argparse.ArgumentParser(description="Simple EmoDubber Inference")
    
    # Required arguments
    parser.add_argument("--text", type=str, required=True,
                       help="Text to synthesize")
    parser.add_argument("--ref_audio_path", type=str, required=True,
                       help="Path to reference audio file")
    parser.add_argument("--output_path", type=str, required=True,
                       help="Output audio file path")
    parser.add_argument("--model_checkpoint", type=str, required=True,
                       help="Path to EmoDubber model checkpoint")
    parser.add_argument("--vocoder_checkpoint", type=str, required=True,
                       help="Path to vocoder checkpoint")
    
    # Optional arguments
    parser.add_argument("--device", type=str, default="auto",
                       choices=["auto", "cuda", "cpu"],
                       help="Device to use for inference")
    parser.add_argument("--temperature", type=float, default=0.667,
                       help="Sampling temperature (default: 0.667)")
    parser.add_argument("--steps", type=int, default=10,
                       help="Number of ODE steps (default: 10)")
    parser.add_argument("--speaking_rate", type=float, default=1.0,
                       help="Speaking rate control (default: 1.0)")
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.exists(args.ref_audio_path):
        raise FileNotFoundError(f"Reference audio not found: {args.ref_audio_path}")
    if not os.path.exists(args.model_checkpoint):
        raise FileNotFoundError(f"Model checkpoint not found: {args.model_checkpoint}")
    if not os.path.exists(args.vocoder_checkpoint):
        raise FileNotFoundError(f"Vocoder checkpoint not found: {args.vocoder_checkpoint}")
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    # Initialize dubber
    print("Initializing SimpleDubber...")
    dubber = SimpleDubber(
        model_checkpoint=args.model_checkpoint,
        vocoder_checkpoint=args.vocoder_checkpoint,
        device=args.device
    )
    
    # Synthesize audio
    print("\nSynthesizing audio...")
    output = dubber.synthesize(
        text=args.text,
        ref_audio_path=args.ref_audio_path,
        temperature=args.temperature,
        steps=args.steps,
        speaking_rate=args.speaking_rate
    )
    
    # Save output
    dubber.save_audio(output, args.output_path)
    
    print(f"\n✓ Dubbing completed!")
    print(f"  Input text: '{args.text}'")
    print(f"  Reference audio: {args.ref_audio_path}")
    print(f"  Output audio: {args.output_path}")
    print(f"  RTF: {output['rtf']:.4f}")


if __name__ == "__main__":
    main()
