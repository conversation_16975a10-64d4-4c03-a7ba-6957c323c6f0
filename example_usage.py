#!/usr/bin/env python3
"""
Example usage of the SimpleDubber for easy dubbing inference.

This script demonstrates how to use the SimpleDubber class to dub audio
with just a few lines of code.
"""

from simple_dubbing_inference import SimpleDubber
import os

def example_basic_usage():
    """Basic example of using SimpleDubber."""
    
    # Paths to your model checkpoints
    model_checkpoint = "Pretrained_TTSmodel/TTS_model.ckpt"
    vocoder_checkpoint = "Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder"
    
    # Check if checkpoints exist
    if not os.path.exists(model_checkpoint):
        print(f"❌ Model checkpoint not found: {model_checkpoint}")
        print("Please download the pretrained model first.")
        return
        
    if not os.path.exists(vocoder_checkpoint):
        print(f"❌ Vocoder checkpoint not found: {vocoder_checkpoint}")
        print("Please download the vocoder first.")
        return
    
    # Initialize the dubber
    print("🚀 Initializing SimpleDubber...")
    dubber = SimpleDubber(
        model_checkpoint=model_checkpoint,
        vocoder_checkpoint=vocoder_checkpoint,
        device="auto"  # Automatically choose GPU if available
    )
    
    # Example 1: Simple dubbing
    print("\n📝 Example 1: Basic dubbing")
    text = "Hello, this is a test of the dubbing system."
    ref_audio = "man1.mp3"  # Your reference audio file
    
    if os.path.exists(ref_audio):
        output = dubber.synthesize(
            text=text,
            ref_audio_path=ref_audio,
            temperature=0.7,  # Controls randomness
            steps=10,         # Quality vs speed tradeoff
            speaking_rate=1.0 # Normal speaking speed
        )
        
        # Save the result
        dubber.save_audio(output, "output_example1.wav")
        print(f"✅ Generated audio saved to output_example1.wav")
    else:
        print(f"❌ Reference audio not found: {ref_audio}")
    
    # Example 2: Different parameters
    print("\n📝 Example 2: Slower speech with higher quality")
    text2 = "This is a slower, higher quality example."
    
    if os.path.exists(ref_audio):
        output2 = dubber.synthesize(
            text=text2,
            ref_audio_path=ref_audio,
            temperature=0.5,  # Less randomness for more stable output
            steps=20,         # More steps for higher quality
            speaking_rate=1.2 # Slower speech
        )
        
        dubber.save_audio(output2, "output_example2.wav")
        print(f"✅ Generated audio saved to output_example2.wav")


def example_batch_processing():
    """Example of processing multiple texts with the same reference."""
    
    model_checkpoint = "Pretrained_TTSmodel/TTS_model.ckpt"
    vocoder_checkpoint = "Vocoder_16KHz/g_EmoDubber_16KHz_Vocoder"
    ref_audio = "man1.mp3"
    
    # Check prerequisites
    if not all(os.path.exists(p) for p in [model_checkpoint, vocoder_checkpoint, ref_audio]):
        print("❌ Missing required files for batch processing example")
        return
    
    # Initialize dubber once
    dubber = SimpleDubber(model_checkpoint, vocoder_checkpoint)
    
    # List of texts to process
    texts = [
        "Good morning, how are you today?",
        "The weather is beautiful outside.",
        "Let's go for a walk in the park.",
        "Thank you for your time and attention."
    ]
    
    print(f"\n🔄 Processing {len(texts)} texts...")
    
    for i, text in enumerate(texts, 1):
        print(f"Processing {i}/{len(texts)}: '{text[:30]}...'")
        
        output = dubber.synthesize(
            text=text,
            ref_audio_path=ref_audio,
            temperature=0.667,
            steps=10,
            speaking_rate=1.0
        )
        
        output_path = f"batch_output_{i:02d}.wav"
        dubber.save_audio(output, output_path)
        print(f"  ✅ Saved to {output_path}")


def main():
    """Run the examples."""
    print("🎬 EmoDubber Simple Inference Examples")
    print("=" * 50)
    
    try:
        # Run basic usage example
        example_basic_usage()
        
        # Run batch processing example
        example_batch_processing()
        
        print("\n🎉 All examples completed successfully!")
        print("\nNext steps:")
        print("1. Replace dummy speaker/visual features with real extractors")
        print("2. Add your own reference audio files")
        print("3. Experiment with different temperature and step values")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("Make sure you have:")
        print("- Downloaded the pretrained models")
        print("- Installed all required dependencies")
        print("- Have a reference audio file (man1.mp3)")


if __name__ == "__main__":
    main()
